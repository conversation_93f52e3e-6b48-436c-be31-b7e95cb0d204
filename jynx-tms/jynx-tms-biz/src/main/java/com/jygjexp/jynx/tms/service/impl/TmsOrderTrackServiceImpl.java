package com.jygjexp.jynx.tms.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.jygjexp.jynx.common.core.util.R;
import com.jygjexp.jynx.common.security.util.SecurityUtils;
import com.jygjexp.jynx.tms.api.feign.RemoteTmsReturnService;
import com.jygjexp.jynx.tms.constants.TrackTypeConstant;
import com.jygjexp.jynx.tms.dto.TmsLatitudeAndLongitudeDto;
import com.jygjexp.jynx.tms.dto.TmsOrderPathDto;
import com.jygjexp.jynx.tms.entity.*;
import com.jygjexp.jynx.tms.enums.NewOrderStatus;
import com.jygjexp.jynx.tms.mapper.*;
import com.jygjexp.jynx.tms.response.LocalizedR;
import com.jygjexp.jynx.tms.service.TmsOrderTrackService;
import com.jygjexp.jynx.tms.utils.OkHttpUtil;
import com.jygjexp.jynx.tms.utils.OrderTools;
import com.jygjexp.jynx.tms.vo.TmsOrderTrackVo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.http.ResponseEntity;
import org.springframework.http.client.SimpleClientHttpRequestFactory;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.io.IOException;
import java.math.BigDecimal;
import java.net.InetSocketAddress;
import java.net.Proxy;
import java.time.Duration;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 卡派-订单节点轨迹
 *
 * <AUTHOR>
 * @date 2025-03-14 17:23:05
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class TmsOrderTrackServiceImpl extends ServiceImpl<TmsOrderTrackMapper, TmsOrderTrackEntity> implements TmsOrderTrackService {

    public static final String GEOCODE_CITY_API ="https://maps.googleapis.com/maps/api/geocode/json?latlng=%s,%s&key=AIzaSyAJPHMzFIOVgBiyukRDkG9J91LVEDZvpDk";
    @Value("${spring.profiles.active:test}")
    private String activeProfile;

    // 佳邮轨迹查询接口配置
    private static final String JY_API_URL = "http://api.jygjexp.com/v1/api/tracking/query/trackNB";
    private static final String JY_API_KEY = "675bfe2fd67105e9a88e564bf0f0344c";


    private final TmsOrderTrackMapper orderTrackMapper;
    private final TmsCarrierMapper carrierMapper;
    private final RemoteTmsReturnService remoteTmsReturnService;
    private final TmsCustomerOrderMapper customerOrderMapper;
    private final TmsEntrustedOrderMapper entrustedOrderMapper;
    private final TmsDriverMapper driverMapper;
    private final TmsLmdDriverMapper tmsLmdDriverMapper;
    private final TmsTrackNodeMapper  trackNodeMapper;
    private final TmsCustomerMapper customerMapper;
    private final TmsThirdPartPostMapper tmsThirdPartPostMapper;


    // 手动插入轨迹方法
    @Override
    public void saveTrack(String orderNo,String customerNo, String orderStatus, String site, String locationDescription,String externalDescription,Integer trackType) {
        // 先校验轨迹是否存在
        boolean trackExists = existsTrack(orderNo, locationDescription,orderStatus);
        if (!trackExists) {
            log.info("轨迹保存开始，订单号：{}", orderNo);

            // 获取当前登录用户信息（兼容 API 推送）
            Long staffId = 0L;
            String phone = "";

            if (SecurityUtils.getUser() != null) {  // **如果是系统内用户**
                staffId = SecurityUtils.getUser().getId();
                phone = SecurityUtils.getUser().getPhone();
            }

            // 获取司机当前经纬度信息
            Long driverId = 0L;
            BigDecimal lat = BigDecimal.ZERO;
            BigDecimal lng = BigDecimal.ZERO;

            if (StrUtil.isNotBlank(phone)) { // **只有系统用户才能查司机位置**
                MPJLambdaWrapper<TmsLmdDriverEntity> wrapper = new MPJLambdaWrapper<>();
                wrapper.select(TmsLmdDriverEntity::getDriverId)
                        .select(TmsDriverLocationEntity::getLatitude, TmsDriverLocationEntity::getLongitude);
                wrapper.leftJoin(TmsDriverLocationEntity.class, TmsDriverLocationEntity::getDriverId, TmsLmdDriverEntity::getDriverId);
                wrapper.eq(TmsLmdDriverEntity::getPhone, phone);

                TmsLatitudeAndLongitudeDto tmsDriver = tmsLmdDriverMapper.selectJoinOne(TmsLatitudeAndLongitudeDto.class, wrapper);

                driverId = Optional.ofNullable(tmsDriver).map(TmsLatitudeAndLongitudeDto::getDriverId).orElse(0L);
                lat = Optional.ofNullable(tmsDriver).map(TmsLatitudeAndLongitudeDto::getLatitude).orElse(new BigDecimal(0));
                lng = Optional.ofNullable(tmsDriver).map(TmsLatitudeAndLongitudeDto::getLongitude).orElse(new BigDecimal(0));
            }

            // 根据节点状态名称获取状态码
            Integer code = NewOrderStatus.getCodeByName(orderStatus);

            // 根据经纬度获取城市信息
            String city = getCityFromCoordinates(lat, lng);
            if (StrUtil.isBlank(city)) {
                //String city1 = searchCity(lat, lng);
//            if (StrUtil.isNotBlank(city1)) {
//                city = city1;
//            }
                city = "Vancouver";
            }

            // 根据客户单号获取承运商信息（暂时不做操作）
            String carrierName = "";

            // 判断是否为主单号
            boolean isMasterOrder = checkIfMasterOrder(orderNo);

            if (isMasterOrder) {
                // 查询主单号的所有主子单号
                List<TmsCustomerOrderEntity> subOrderNos = customerOrderMapper.selectList(
                        new LambdaQueryWrapper<TmsCustomerOrderEntity>()
                                .likeRight(TmsCustomerOrderEntity::getEntrustedOrderNumber, orderNo));


                if (CollUtil.isNotEmpty(subOrderNos)) {
                    // 轨迹列表
                    List<TmsOrderTrackEntity> trackList = new ArrayList<>();

                    // 创建轨迹（包括主单和子单）
                    for (TmsCustomerOrderEntity subOrder : subOrderNos) {
                        TmsOrderTrackEntity subPath = new TmsOrderPathDto().createPath(subOrder.getEntrustedOrderNumber(), customerNo, orderStatus, "",
                                locationDescription, externalDescription, trackType, carrierName, staffId, driverId, city, code, "");
                        trackList.add(subPath);
                    }

                    // 批量插入轨迹
                    if (!trackList.isEmpty()) {
                        this.saveBatch(trackList);
                    }

                    log.info("轨迹保存成功，主单号：{}，子单号数量：{}", orderNo, subOrderNos.size());
                } else {
                    // 直接新增一条轨迹
                    TmsOrderTrackEntity mainPath = new TmsOrderPathDto().createPath(orderNo, customerNo, orderStatus, "",
                            locationDescription, externalDescription, trackType, carrierName, staffId, driverId, city, code, "");
                    orderTrackMapper.insert(mainPath);
                    log.info("轨迹保存成功，单号：{}", orderNo);
                }

            } else {
                // 如果是子单号，直接新增一条记录
                TmsOrderTrackEntity path = new TmsOrderPathDto().createPath(orderNo, customerNo, orderStatus, "",
                        locationDescription, externalDescription, trackType, carrierName, staffId, driverId, city, code, "");

                orderTrackMapper.insert(path);
                log.info("轨迹保存成功，单号：{}", orderNo);
            }
        }
    }

    // 根据触发节点插入轨迹方法
    @Override
    public void saveTrack(String orderNo, String customerNo, String orderStatus, Integer trackLink) {
        // 根据节点触发环节，查询对应映射轨迹
        TmsTrackNodeEntity tmsTrackNodeEntities = trackNodeMapper.selectOne(new LambdaQueryWrapper<TmsTrackNodeEntity>()
                .eq(TmsTrackNodeEntity::getNodeLink, trackLink)
                .eq(TmsTrackNodeEntity::getIsValid, 1)
                .last("limit 1"));

        // 先校验轨迹是否存在
        boolean trackExists = existsTrack(orderNo, tmsTrackNodeEntities.getNodeContent(),orderStatus);
        if (!trackExists) {
            if (ObjectUtil.isNotNull(tmsTrackNodeEntities)) {
                log.info("轨迹保存开始，订单号：{}", orderNo);
                // 获取轨迹类型和轨迹描述内容
                Integer trackType = tmsTrackNodeEntities.getTrackType();   // 轨迹类型 0:内外部 1：内部
                String locationDescription = tmsTrackNodeEntities.getNodeContent();    // 内部节点内容
                String externalDescription = tmsTrackNodeEntities.getNodeOutContent();  // 外部节点内容
                Integer code = tmsTrackNodeEntities.getNodeCode();   // 节点状态码

                // 获取节点名称
                String nodeName = tmsTrackNodeEntities.getNodeName();

                // 获取当前登录用户信息（兼容 API 推送）
                Long staffId = 0L;
                String phone = "";

                if (SecurityUtils.getUser() != null) {  // **如果是系统内用户**
                    staffId = SecurityUtils.getUser().getId();
                    phone = SecurityUtils.getUser().getPhone();
                }

                // 获取司机当前经纬度信息
                Long driverId = 0L;
                BigDecimal lat = BigDecimal.ZERO;
                BigDecimal lng = BigDecimal.ZERO;

                if (StrUtil.isNotBlank(phone)) { // **只有系统用户才能查司机位置**
                    MPJLambdaWrapper<TmsLmdDriverEntity> wrapper = new MPJLambdaWrapper<>();
                    wrapper.select(TmsLmdDriverEntity::getDriverId)
                            .select(TmsDriverLocationEntity::getLatitude, TmsDriverLocationEntity::getLongitude);
                    wrapper.leftJoin(TmsDriverLocationEntity.class, TmsDriverLocationEntity::getDriverId, TmsLmdDriverEntity::getDriverId);
                    wrapper.eq(TmsLmdDriverEntity::getPhone, phone);

                    TmsLatitudeAndLongitudeDto tmsDriver = tmsLmdDriverMapper.selectJoinOne(TmsLatitudeAndLongitudeDto.class, wrapper);

                    driverId = Optional.ofNullable(tmsDriver).map(TmsLatitudeAndLongitudeDto::getDriverId).orElse(0L);
                    lat = Optional.ofNullable(tmsDriver).map(TmsLatitudeAndLongitudeDto::getLatitude).orElse(new BigDecimal(0));
                    lng = Optional.ofNullable(tmsDriver).map(TmsLatitudeAndLongitudeDto::getLongitude).orElse(new BigDecimal(0));
                }

                // 根据节点状态名称获取状态码
                // Integer code = NewOrderStatus.getCodeByName(orderStatus);

                // 根据经纬度获取城市信息
                String city = "";
                if (lat.compareTo(BigDecimal.ZERO) != 0 && lng.compareTo(BigDecimal.ZERO) != 0) {
                    city = getCityFromCoordinates(lat, lng);
                    if (StrUtil.isBlank(city)) {
                        String city1 = searchCity(lat, lng);
                        if (StrUtil.isNotBlank(city1)) {
                            city = city1;
                        }
                    }
                }

                // 根据客户单号获取承运商信息（暂时不做操作）
                String carrierName = "";

                // 判断是否为主单号
                boolean isMasterOrder = checkIfMasterOrder(orderNo);

                if (isMasterOrder) {
                    // 查询主单号的所有主子单号
                    List<TmsCustomerOrderEntity> subOrderNos = customerOrderMapper.selectList(
                            new LambdaQueryWrapper<TmsCustomerOrderEntity>()
                                    .likeRight(TmsCustomerOrderEntity::getEntrustedOrderNumber, orderNo));


                    if (CollUtil.isNotEmpty(subOrderNos)) {
                        // 轨迹列表
                        List<TmsOrderTrackEntity> trackList = new ArrayList<>();

                        // 创建轨迹（包括主单和子单）
                        for (TmsCustomerOrderEntity subOrder : subOrderNos) {
                            TmsOrderTrackEntity subPath = new TmsOrderPathDto().createPath(subOrder.getEntrustedOrderNumber(), customerNo, orderStatus, "",
                                    locationDescription, externalDescription, trackType, carrierName, staffId, driverId, city, code, nodeName);
                            trackList.add(subPath);
                        }

                        // 批量插入轨迹
                        if (!trackList.isEmpty()) {
                            this.saveBatch(trackList);
                        }

                        log.info("轨迹保存成功，主单号：{}，子单号数量：{}", orderNo, subOrderNos.size());
                    } else {
                        // 直接新增一条轨迹
                        TmsOrderTrackEntity mainPath = new TmsOrderPathDto().createPath(orderNo, customerNo, orderStatus, "",
                                locationDescription, externalDescription, trackType, carrierName, staffId, driverId, city, code, nodeName);
                        orderTrackMapper.insert(mainPath);
                        log.info("轨迹保存成功，单号：{}", orderNo);
                    }

                } else {
                    // 如果是子单号，直接新增一条记录
                    TmsOrderTrackEntity path = new TmsOrderPathDto().createPath(orderNo, customerNo, orderStatus, "",
                            locationDescription, externalDescription, trackType, carrierName, staffId, driverId, city, code, nodeName);

                    orderTrackMapper.insert(path);
                    log.info("轨迹保存成功，单号：{}", orderNo);
                }
            }
        }
    }

    // 校验轨迹是否存在
    public boolean existsTrack(String orderNo, String locationDescription, String orderStatus) {
        LambdaQueryWrapper<TmsOrderTrackEntity> wrapper = new LambdaQueryWrapper<>();
        // 判断主单 or 子单
        if (orderNo.length() < 15) {
            // 主单：使用 likeRight 匹配所有子单轨迹
            wrapper.likeRight(TmsOrderTrackEntity::getOrderNo, orderNo);
        } else {
            // 子单：精确匹配
            wrapper.eq(TmsOrderTrackEntity::getOrderNo, orderNo);
        }

        wrapper.eq(TmsOrderTrackEntity::getOrderStatus, orderStatus)
                .like(TmsOrderTrackEntity::getLocationDescription, locationDescription);

        return orderTrackMapper.selectCount(wrapper) > 0;
    }

    // 根据经纬度查询城市信息
    @Override
    public String searchCity(BigDecimal lat, BigDecimal lng) {
        String geocode = remoteTmsReturnService.getfindResult(String.format("%.3f,%.3f", lat, lng));
        String api;
        String ret;
        String city="Vancouver";
        if (StringUtils.isBlank(geocode)) {
            api = String.format(GEOCODE_CITY_API, String.format("%.6f", lat), String.format("%.6f", lng));

            // 设置代理
            Proxy proxy = new Proxy(Proxy.Type.HTTP, new InetSocketAddress("*************", 7890));
            SimpleClientHttpRequestFactory requestFactory = new SimpleClientHttpRequestFactory();
            if ("dev".equals(activeProfile)) {
                requestFactory.setProxy(proxy);
            }
            // 创建 RestTemplate 并注入代理工厂
            RestTemplate restTemplate = new RestTemplate(requestFactory);
            ResponseEntity<String> response = restTemplate.getForEntity(api, String.class);

            if (response.getStatusCode().is2xxSuccessful()) {
                ret = response.getBody();
                city = extractCityFromJson(ret);
                com.alibaba.fastjson.JSONObject jo = JSON.parseObject(ret);
                if (null != jo) {
                    com.alibaba.fastjson.JSONArray results = jo.getJSONArray("results");
                    if (results.size() > 0) {
                        Map<String, String> paramMap = new HashMap<>();
                        paramMap.put("keyword", String.format("%.3f,%.3f", lat, lng));
                        paramMap.put("result", ret);
                        remoteTmsReturnService.addGmapResult(paramMap);
                    }
                }
            }
        } else {
            ret = geocode;
            city = extractCityFromJson(ret);
        }
        return city==null?"Canada":city;
    }

    // 根据客户单号删除轨迹
    @Override
    public Boolean deleteByCustomerOrderNo(String customerOrderNo) {
        // 根据客户单号查询出轨迹列表
        List<TmsOrderTrackEntity> trackList = orderTrackMapper.selectList(
                new LambdaQueryWrapper<TmsOrderTrackEntity>()
                        .eq(TmsOrderTrackEntity::getCustomerOrderNo, customerOrderNo));

        // 提取出全部id
        List<Long> trackIds = trackList.stream().map(TmsOrderTrackEntity::getTrackId).collect(Collectors.toList());

        // 批量删除轨迹
        if (!trackIds.isEmpty()) {
            orderTrackMapper.deleteBatchIds(trackIds);
            return Boolean.TRUE;
        }
        return Boolean.FALSE;
    }


    /**
     * 从 Google Maps API 的 JSON 响应中提取城市名
     * @param jsonResponse Google Maps Geocoding API 的响应字符串
     * @return 城市名称（如果找到），否则返回 null
     */
    public static String extractCityFromJson(String jsonResponse) {
        try {
            ObjectMapper objectMapper = new ObjectMapper();
            JsonNode root = objectMapper.readTree(jsonResponse);
            JsonNode results = root.path("results");

            if (results.isArray() && results.size() > 0) {
                for (JsonNode result : results) {
                    JsonNode addressComponents = result.path("address_components");
                    for (JsonNode component : addressComponents) {
                        JsonNode types = component.path("types");
                        if (types.isArray()) {
                            for (JsonNode type : types) {
                                String typeStr = type.asText();
                                if ("locality".equals(typeStr) || "administrative_area_level_1".equals(typeStr)) {
                                    return component.path("long_name").asText();
                                }
                            }
                        }
                    }
                }
            }
        } catch (Exception e) {
            System.err.println("解析城市失败：" + e.getMessage());
        }

        return null;
    }



    /**
     * 判断是否为主单号的方法
     * 规则：主单号长度为13位，子单号长度是16位
     */
    private boolean checkIfMasterOrder(String orderNo) {
        return orderNo.trim().length() <= 14;
    }


    // 加拿大六大常用城市城市经纬度范围映射
    private static final Map<String, BigDecimal[]> CITY_BOUNDS = new HashMap<>();

    static {
        CITY_BOUNDS.put("Calgary", new BigDecimal[]{new BigDecimal("50.8429"), new BigDecimal("51.2121"), new BigDecimal("-114.3150"), new BigDecimal("-113.8561")});
        CITY_BOUNDS.put("Vancouver", new BigDecimal[]{new BigDecimal("49.1985"), new BigDecimal("49.3168"), new BigDecimal("-123.2247"), new BigDecimal("-123.0232")});
        CITY_BOUNDS.put("Montreal", new BigDecimal[]{new BigDecimal("45.4000"), new BigDecimal("45.7000"), new BigDecimal("-73.9500"), new BigDecimal("-73.5000")});
        CITY_BOUNDS.put("Toronto", new BigDecimal[]{new BigDecimal("43.5800"), new BigDecimal("43.8555"), new BigDecimal("-79.6393"), new BigDecimal("-79.1150")});
        CITY_BOUNDS.put("Ottawa", new BigDecimal[]{new BigDecimal("45.2100"), new BigDecimal("45.5400"), new BigDecimal("-75.9400"), new BigDecimal("-75.2000")});
        CITY_BOUNDS.put("Edmonton", new BigDecimal[]{new BigDecimal("53.3600"), new BigDecimal("53.7167"), new BigDecimal("-113.7250"), new BigDecimal("-113.2085")});
    }

    /**
     * 判断经纬度是否属于某个城市
     * @param lat 纬度
     * @param lng 经度
     * @return 城市名称（如果匹配），否则返回 null
     */

    public static String getCityFromCoordinates(BigDecimal lat, BigDecimal lng) {
        if (lat == null || lng == null) {
            return "";
        }

        for (Map.Entry<String, BigDecimal[]> entry : CITY_BOUNDS.entrySet()) {
            BigDecimal[] bounds = entry.getValue();
            BigDecimal minLat = bounds[0], maxLat = bounds[1], minLng = bounds[2], maxLng = bounds[3];

            if (lat.compareTo(minLat) >= 0 && lat.compareTo(maxLat) <= 0 && lng.compareTo(minLng) >= 0 && lng.compareTo(maxLng) <= 0) {
                return entry.getKey();
            }
        }
        return "";
    }



    // 分页查询
    @Override
    public Page<TmsOrderTrackVo> search(Page page, TmsOrderTrackVo vo) {
        MPJLambdaWrapper<TmsOrderTrackEntity> wrapper = new MPJLambdaWrapper<>();

        // 获取当前查询的订单号（箱单号）
        String orderNo = (vo.getOrderNo() == null) ? "0000" : vo.getOrderNo();
        wrapper.selectAll(TmsOrderTrackEntity.class);
        wrapper.select(TmsLmdDriverEntity::getDriverName);
        wrapper.eq(TmsOrderTrackEntity::getOrderNo, orderNo);
        wrapper.in(TmsOrderTrackEntity::getTrackType, Arrays.asList(TrackTypeConstant.EXTERNAL, TrackTypeConstant.INTERIOR));
        wrapper.leftJoin(TmsLmdDriverEntity.class,TmsLmdDriverEntity::getDriverId, TmsOrderTrackEntity::getDriverId);
        wrapper.orderByDesc(TmsOrderTrackEntity::getAddTime);


        return orderTrackMapper.selectJoinPage(page, TmsOrderTrackVo.class, wrapper);
    }



    // 获取最大状态码
    @Override
    public TmsOrderTrackEntity getMaxStatusCodeByOrderNo(String orderNo) {
        if (orderNo.length() < 15){
            orderNo = orderNo+"001";
        }
        LambdaQueryWrapper<TmsOrderTrackEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.select(TmsOrderTrackEntity::getStatusCode)
                .eq(TmsOrderTrackEntity::getOrderNo, orderNo)
                .orderByDesc(TmsOrderTrackEntity::getStatusCode)
                .last("LIMIT 1");
        TmsOrderTrackEntity entity = orderTrackMapper.selectOne(wrapper);
        return entity;
    }

















    // 查询ups轨迹 ----------------------------------------------------------------------------

    private static final String CLIENT_ID = "MBe6tvv7FPboX2d7AMxXyKvQj5AW0gxGstJfuSm8zwbI6T6t";
    private static final String CLIENT_SECRET = "ZjMo0LHJq04LlHTAWatXhYPiZCfOARWTjJij68YkwPGNsMTXhu1FsZKc13Xq7Ov4";
    private static final String CLIENT_ID_XB = "IUR2AIPQ1Qyl9bDOZOoQuU0TzfEcDtUKKRhg4qAizGTLDRC5";
    private static final String CLIENT_SECRET_XB = "4uQHtS1A5EfACgzMhgnAgAxA1puXOod95FdkvoSNGHBLJHeG23BXKGQKwzrTQOii";


    private static final String TOKEN_URL = "https://onlinetools.ups.com/security/v1/oauth/token";               // 测试环境：https://wwwcie.ups.com/security/v1/oauth/token
    private static final String TRACK_URL = "https://onlinetools.ups.com/api/track/v1/details/";                // 生产环境：https://onlinetools.ups.com/api/track/v1/details/{inquiryNumber}
    private static final boolean USE_PROXY = false;
    private static final String PROXY_HOST = "*************";
    private static final int PROXY_PORT = 7890;

    //private final RedisTemplate<String, Object> redisTemplate;
    private final StringRedisTemplate stringRedisTemplate;
    private OkHttpClient client;
    private final ObjectMapper objectMapper = new ObjectMapper();

    private OkHttpClient getClient() {
        if (client == null) {
            OkHttpClient.Builder builder = new OkHttpClient.Builder();
            if (USE_PROXY) {
                Proxy proxy = new Proxy(Proxy.Type.HTTP, new InetSocketAddress(PROXY_HOST, PROXY_PORT));
                builder.proxy(proxy);
            }
            client = builder.build();
        }
        return client;
    }

    /**
     * 获取 UPS Access Token（带 Redis 缓存）
     */
    public String getAccessTokenWithCache() {
        String cacheKey = "UPS_NEW:AccessToken";

        // 直接从缓存获取字符串，无需类型转换
        String token = stringRedisTemplate.opsForValue().get(cacheKey);

        if (StrUtil.isNotBlank(token)) {
            return token;
        }

        String credentials = Base64.getEncoder().encodeToString((CLIENT_ID + ":" + CLIENT_SECRET).getBytes());

        RequestBody body = new FormBody.Builder()
                .add("grant_type", "client_credentials")
                .build();

        Request request = new Request.Builder()
                .url(TOKEN_URL)
                .addHeader("Authorization", "Basic " + credentials)
                .addHeader("Content-Type", "application/x-www-form-urlencoded")
                .post(body)
                .build();

        try (Response response = getClient().newCall(request).execute()) {
            String json = response.body().string();
            if (!response.isSuccessful()) {
                throw new RuntimeException("Token request failed: " + json);
            }

            JsonNode root = objectMapper.readTree(json);
            String accessToken = root.get("access_token").asText();
            int expiresIn = root.get("expires_in").asInt();

            // 使用 StringRedisTemplate 设置值和过期时间
            stringRedisTemplate.opsForValue().set(
                    cacheKey,
                    accessToken,
                    Duration.ofSeconds(expiresIn - 300)
            );

            return accessToken;
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }



    @Override
    public R queryUpsTracking(String inquiryNumber) {
        String url = TRACK_URL + inquiryNumber + "?locale=en_US&returnSignature=false";

        String token = getAccessTokenWithCache();

        Request request = new Request.Builder()
                .url(url)
                .addHeader("Authorization", "Bearer " + token)
                .addHeader("transId", UUID.randomUUID().toString())
                .addHeader("transactionSrc", "NB")
                .addHeader("Content-Type", "application/json")
                .get()
                .build();

        try (Response response = getClient().newCall(request).execute()) {
            String json = response.body().string();
            if (!response.isSuccessful()) {
                throw new IOException("Track request failed: " + json);
            }
            JsonNode jsonNode = objectMapper.readTree(json);
            System.out.println(jsonNode);
            return R.ok(jsonNode);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }


    @Override
    public String queryUpsTrackingForJson(String inquiryNumber) {
        String url = TRACK_URL + inquiryNumber + "?locale=en_US&returnSignature=false";

        String token = getAccessTokenWithCache();

        Request request = new Request.Builder()
                .url(url)
                .addHeader("Authorization", "Bearer " + token)
                .addHeader("transId", UUID.randomUUID().toString())
                .addHeader("transactionSrc", "NB")
                .addHeader("Content-Type", "application/json")
                .get()
                .build();
        try (Response response = getClient().newCall(request).execute()) {
            String json = response.body().string();
            if (!response.isSuccessful()) {
             return "error";
            }
          return json;
        } catch (IOException e) {
            return "error";
        }
    }


    /**
     * 提取轨迹概要信息
     */
    public void printTrackingInfo(JsonNode response) {
        try {
            JsonNode shipment = response.path("trackResponse").path("shipment").get(0);
            JsonNode pkg = shipment.path("package").get(0);
            String trackingNumber = pkg.path("trackingNumber").asText();
            JsonNode activity = pkg.path("activity").get(0);
            String status = activity.path("status").path("description").asText();
            String date = activity.path("date").asText();
            String time = activity.path("time").asText();
            System.out.printf("Tracking: %s\nStatus: %s\nDate: %s\nTime: %s\n",
                    trackingNumber, status, date, time);
        } catch (Exception e) {
            System.err.println("Failed to parse tracking response: " + e.getMessage());
        }
    }

    // 17track查询Nb中大件轨迹
    @Override
    public R getSqTrack(String orderNo) {
        // 1. 查询订单信息
        LambdaQueryWrapper<TmsCustomerOrderEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(TmsCustomerOrderEntity::getDelFlag, "0")
                .eq(TmsCustomerOrderEntity::getSubFlag, true)
                .and(wrapper -> wrapper
                        .eq(TmsCustomerOrderEntity::getEntrustedOrderNumber, orderNo)
                        .or()
                        .eq(TmsCustomerOrderEntity::getCustomerOrderNumber, orderNo));

        TmsCustomerOrderEntity customerOrder = customerOrderMapper.selectOne(queryWrapper, false);
        if (ObjectUtil.isNull(customerOrder)) {
            return R.failed("No order information was found");
        }

        // 2. 检查换单记录
        String mainOrderNo = customerOrder.getEntrustedOrderNumber();
        TmsThirdPartPostEntity thirdPartOrder = tmsThirdPartPostMapper.selectOne(
                new LambdaQueryWrapper<TmsThirdPartPostEntity>()
                        .eq(TmsThirdPartPostEntity::getNbOrderNo, mainOrderNo),
                false);

        // 3. 处理UNI轨迹（如果存在且满足条件）
        if (ObjectUtil.isNotNull(thirdPartOrder) && StrUtil.isNotBlank(thirdPartOrder.getChannelOrderNo())) {
            Map<String, Object> uniTrackResult = querySmallPackageTrack(thirdPartOrder.getChannelOrderNo());

            // 检查UNI轨迹是否有足够的事件（≥2条）
            if (uniTrackResult.containsKey("events")) {
                List<Map<String, Object>> events = (List<Map<String, Object>>) uniTrackResult.get("events");
                if (CollUtil.isNotEmpty(events) && events.size() >= 2) {
                    return R.ok(uniTrackResult);
                }
            }
            // 不满足条件则继续查询NB轨迹
        }

        // 4. 查询NB轨迹
        List<TmsOrderTrackEntity> allTracks = orderTrackMapper.selectList(
                new LambdaQueryWrapper<TmsOrderTrackEntity>()
                        .eq(TmsOrderTrackEntity::getOrderNo, mainOrderNo)
                        .eq(TmsOrderTrackEntity::getDelFlag, "0")
                        .eq(TmsOrderTrackEntity::getTrackType, TrackTypeConstant.EXTERNAL)
                        .orderByDesc(TmsOrderTrackEntity::getAddTime));

        if (CollUtil.isEmpty(allTracks)) {
            return R.ok(Collections.emptyMap());
        }

        // 5. 构建NB轨迹返回结果
        Map<String, Object> result = new LinkedHashMap<>();
        List<Map<String, Object>> events = new ArrayList<>();
        for (TmsOrderTrackEntity track : allTracks) {
            Map<String, Object> event = new LinkedHashMap<>();
            event.put("location", StrUtil.blankToDefault(track.getCity(), ""));
            event.put("time", DateUtil.format(track.getAddTime(), "yyyy-MM-dd HH:mm:ss"));
            event.put("content", StrUtil.blankToDefault(track.getLocationDescription(), ""));
            events.add(event);
        }

        result.put("number", mainOrderNo);
        result.put("oriNumber", mainOrderNo);
        result.put("destCountry", StrUtil.blankToDefault(customerOrder.getDestination(), ""));
        result.put("oriCountry", StrUtil.blankToDefault(customerOrder.getOrigin(), ""));
        result.put("oriChannel", "Neighbour Express");
        result.put("events", events);
        result.put("status", StrUtil.blankToDefault(allTracks.get(0).getOrderStatus(), ""));

        return R.ok(result);
    }


    /**
     * 查询小包轨迹封装方法
     *
     * @param orderNumbers 小包订单号列表
     * @return Map<运单号, 对应轨迹信息>
     */
    public Map<String, Object> querySmallPackageTrack(String orderNumbers) {
        Map<String, Object> result = new LinkedHashMap<>();
        try {
            String url = "https://api.jygjexp.com/v1/api/tracking/query/trackInfo";
            HashMap<String, String> headerMap = new HashMap<>();
            headerMap.put("code", "220999");
            String apiKey = "198e900a5a66403b86f6c916d05b43ae";
            headerMap.put("apiKey", apiKey);
            headerMap.put("Content-Type", "application/json");

            // 时间戳处理
            LocalDateTime now = LocalDateTime.now();
            if (!System.getProperty("os.name").toLowerCase().contains("win")) {
                now = now.plusHours(12);
            }
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
            headerMap.put("timestamp", now.format(formatter));
            headerMap.put("sign", OrderTools.getMD5("220999" + apiKey));

            String jsonBody = JSONUtil.toJsonStr(Collections.singletonList(orderNumbers));
            String responseStr = OkHttpUtil.doPostJson(url, jsonBody, headerMap);
            JSONObject json = JSONUtil.parseObj(responseStr);

            if (json.getInt("code", 0) == 1 && json.containsKey("data")) {
                JSONArray data = json.getJSONArray("data");

                for (Object obj : data) {
                    JSONObject trackObj = (JSONObject) obj;
                    String trackingNo = trackObj.getStr("trackingNo");
                    JSONArray details = trackObj.getJSONArray("fromDetail");

                    if (CollUtil.isEmpty(details)) continue;

                    // 按时间升序
                    details.sort(Comparator.comparing(o -> ((JSONObject) o).getStr("pathTime")));

                    // 查询对应 NB 子单
                    TmsThirdPartPostEntity thirdPartPost = tmsThirdPartPostMapper.selectOne(
                            new LambdaQueryWrapper<TmsThirdPartPostEntity>()
                                    .eq(TmsThirdPartPostEntity::getChannelOrderNo, trackingNo)
                                    .last("limit 1"));

                    if (thirdPartPost == null) continue;

                    // 构建轨迹事件
                    List<Map<String, Object>> events = new ArrayList<>();
                    for (Object detail : details) {
                        JSONObject d = (JSONObject) detail;
                        Map<String, Object> event = new LinkedHashMap<>();
                        event.put("location", d.getStr("pathLocation"));
                        event.put("time", d.getStr("pathTime"));
                        event.put("content", d.getStr("pathInfo"));
                        events.add(event);
                    }

                    // 判断状态（根据最后一条轨迹内容）
                    JSONObject latest = details.getJSONObject(0);
                    String status = latest.getStr("pathCode");

                    // 构建返回格式
                    result.put("number", thirdPartPost.getNbOrderNo());
                    result.put("oriNumber", trackingNo);
                    result.put("destCountry","Canada");
                    result.put("oriCountry", "中国");
                    result.put("oriChannel", "Neighbour Express");
                    result.put("events", events);
                    result.put("status", status);
                    break;
                }
            }
        } catch (Exception e) {
            log.error("小包轨迹查询失败", e);
        }
        return result;
    }

    // ==================== 迁移自zxoms的轨迹查询接口实现 ====================

    /**
     * 批量订单轨迹查询 - 迁移自zxoms，增加佳邮轨迹查询兼容逻辑
     * @param pkgNos 包裹号列表，逗号分隔
     * @param zipInput 邮编（可选）
     * @return 批量轨迹查询结果
     */
    @Override
    public R getTracksFromZxoms(String pkgNos, String zipInput) {
        if (StrUtil.isBlank(pkgNos)) {
            return R.failed("500300", "pkgNo can not be empty");
        }

        log.info("TMS批量轨迹查询开始，包裹号：{}", pkgNos);

        int maxSize = 50;
        String[] pkgNoArr = pkgNos.trim().split(",");
        if (pkgNoArr.length > maxSize) {
            return R.failed("500301", "Query up to " + maxSize + " packages at a time");
        }

        JSONArray ja = new JSONArray(); // 最终轨迹结果集合
        Set<String> handledPkgNos = new HashSet<>(); // 防止重复添加
        List<String> pkgNoList = Arrays.stream(pkgNoArr).map(String::trim).filter(StrUtil::isNotBlank)
            .distinct()
            .collect(Collectors.toList());

        if (CollUtil.isEmpty(pkgNoList)) {
            return R.failed("500300", "No valid package numbers provided");
        }

        // 分类处理不同类型的订单号
        List<String> gvOrderNos = new ArrayList<>(); // GV开头的订单号，直接查佳邮接口
        List<String> normalAndJyOrderNos = new ArrayList<>(); // NB订单号和JY开头订单号，优先查TMS本地

        for (String pkgNo : pkgNoList) {
            if (pkgNo.startsWith("GV")) {
                gvOrderNos.add(pkgNo);
            } else {
                // JY开头的订单号和NB订单号都先查询本地轨迹
                normalAndJyOrderNos.add(pkgNo);
            }
        }

        // 处理普通订单号和JY开头订单号（优先查询TMS本地轨迹，JY订单无数据则查佳邮）
        if (!normalAndJyOrderNos.isEmpty()) {
            processNormalOrders(normalAndJyOrderNos, zipInput, ja, handledPkgNos);
        }

        // 处理GV开头的订单号（直接查佳邮接口）
        if (!gvOrderNos.isEmpty()) {
            queryJyApiTracks(gvOrderNos, ja, handledPkgNos);
        }

        log.info("TMS批量轨迹查询成功，查询包裹数：{}，返回结果数：{}", pkgNoList.size(), ja.size());
        return R.ok(ja);
    }

    /**
     * 处理普通订单号和JY订单号（优先查询TMS本地轨迹，JY订单无数据则查佳邮）
     * @param orderNos 订单号列表（包含普通订单号和JY开头订单号）
     * @param zipInput 邮编
     * @param ja 结果集合
     * @param handledPkgNos 已处理的包裹号集合
     */
    private void processNormalOrders(List<String> orderNos, String zipInput, JSONArray ja, Set<String> handledPkgNos) {
        // 分离JY开头的订单号，用于后续未查询到本地轨迹时调用佳邮接口
        List<String> jyOrderNos = orderNos.stream()
                .filter(orderNo -> orderNo.startsWith("JY"))
                .collect(Collectors.toList());

        // 查询订单信息
        List<TmsCustomerOrderEntity> orders = customerOrderMapper.selectList(
                new LambdaQueryWrapper<TmsCustomerOrderEntity>()
                        .eq(TmsCustomerOrderEntity::getDelFlag, "0")
                        .and(wrapper -> wrapper
                                .in(TmsCustomerOrderEntity::getEntrustedOrderNumber, orderNos)
                                .or()
                                .in(TmsCustomerOrderEntity::getJyOrderNo, orderNos)
                                .or()
                                .in(TmsCustomerOrderEntity::getCustomerOrderNumber, orderNos)));

        if (CollUtil.isEmpty(orders)) {
            log.warn("NB订单轨迹查询未找到订单，订单号：{}", orderNos);
            return;
        }

        // 处理邮编过滤逻辑
        List<String> zipList = Arrays.stream(StrUtil.nullToEmpty(zipInput).split(","))
                .map(String::trim)
                .filter(StrUtil::isNotBlank)
                .map(String::toUpperCase)
                .collect(Collectors.toList());

        // 邮编过滤逻辑
        boolean hasZipMatch = CollUtil.isNotEmpty(zipList) && orders.stream().anyMatch(order -> {
            String destZip = order.getDestPostalCode();
            return StrUtil.isNotBlank(destZip) && zipList.contains(destZip.toUpperCase());
        });

        // 分别处理普通订单号和JY订单号
        List<String> normalOrderNos = orderNos.stream()
                .filter(orderNo -> !orderNo.startsWith("JY"))
                .collect(Collectors.toList());

        // 处理普通订单号（主单-子单模式）
        if (!normalOrderNos.isEmpty()) {
            processNormalOrdersInternal(orders, normalOrderNos, zipList, hasZipMatch, ja, handledPkgNos);
        }

        // 处理JY订单号（直接匹配模式）
        if (!jyOrderNos.isEmpty()) {
            processJyOrdersInternal(orders, jyOrderNos, zipList, hasZipMatch, ja, handledPkgNos);
        }



        // 对于JY开头的订单号，检查是否有未查询到本地轨迹的，调用佳邮接口查询
        if (!jyOrderNos.isEmpty()) {
            List<String> unhandledJyOrders = jyOrderNos.stream().filter(jyOrderNo -> !handledPkgNos.contains(jyOrderNo)).collect(Collectors.toList());
            if (!unhandledJyOrders.isEmpty()) {
                queryJyApiTracks(unhandledJyOrders, ja, handledPkgNos);
            }
        }
    }

    /**
     * 处理普通订单号的内部方法（主单-子单模式）
     * @param orders 查询到的订单列表
     * @param normalOrderNos 普通订单号列表
     * @param zipList 邮编列表
     * @param hasZipMatch 是否有邮编匹配
     * @param ja 结果集合
     * @param handledPkgNos 已处理的包裹号集合
     */
    private void processNormalOrdersInternal(List<TmsCustomerOrderEntity> orders, List<String> normalOrderNos,
                                           List<String> zipList, boolean hasZipMatch, JSONArray ja, Set<String> handledPkgNos) {
        // 提取主单号
        Set<String> mainOrderNumbers = orders.stream()
                .filter(order -> normalOrderNos.contains(order.getEntrustedOrderNumber()) ||
                               normalOrderNos.contains(order.getJyOrderNo()))
                .map(order -> {
                    if (Boolean.TRUE.equals(order.getSubFlag())) {
                        String subNo = order.getEntrustedOrderNumber();
                        return (subNo != null && subNo.length() >= 15) ? subNo.substring(0, 13) : null;
                    } else {
                        return order.getEntrustedOrderNumber();
                    }
                })
                .filter(StrUtil::isNotBlank)
                .collect(Collectors.toSet());

        // 邮编过滤
        if (hasZipMatch) {
            mainOrderNumbers = orders.stream()
                    .filter(order -> {
                        String destZip = order.getDestPostalCode();
                        return StrUtil.isNotBlank(destZip) && zipList.contains(destZip.toUpperCase());
                    })
                    .filter(order -> normalOrderNos.contains(order.getEntrustedOrderNumber()) ||
                                   normalOrderNos.contains(order.getJyOrderNo()))
                    .map(order -> {
                        if (Boolean.TRUE.equals(order.getSubFlag())) {
                            String subNo = order.getEntrustedOrderNumber();
                            return (subNo != null && subNo.length() >= 15) ? subNo.substring(0, 13) : null;
                        } else {
                            return order.getEntrustedOrderNumber();
                        }
                    })
                    .filter(StrUtil::isNotBlank)
                    .collect(Collectors.toSet());
        }

        // 处理每个主单号的轨迹
        for (String orderNo : mainOrderNumbers) {
            // 查询所有匹配主单号的子单
            LambdaQueryWrapper<TmsCustomerOrderEntity> subQueryWrapper = new LambdaQueryWrapper<>();
            subQueryWrapper.eq(TmsCustomerOrderEntity::getSubFlag, true);
            subQueryWrapper.likeRight(TmsCustomerOrderEntity::getEntrustedOrderNumber, orderNo);
            List<TmsCustomerOrderEntity> subOrders = customerOrderMapper.selectList(subQueryWrapper);
            if (CollUtil.isEmpty(subOrders)) {
                continue;
            }

            // 提取子单号
            List<String> subOrderNumbers = subOrders.stream()
                    .map(TmsCustomerOrderEntity::getEntrustedOrderNumber)
                    .distinct()
                    .collect(Collectors.toList());

            // 查询所有子单轨迹
            List<TmsOrderTrackEntity> tracks = orderTrackMapper.selectList(new LambdaQueryWrapper<TmsOrderTrackEntity>()
                    .in(TmsOrderTrackEntity::getOrderNo, subOrderNumbers)
                    .eq(TmsOrderTrackEntity::getDelFlag, "0")
                    .eq(TmsOrderTrackEntity::getTrackType, TrackTypeConstant.EXTERNAL)
                    .orderByAsc(TmsOrderTrackEntity::getAddTime)
            );

            handledPkgNos.add(orderNo);
            JSONObject orderJo = new JSONObject();
            orderJo.set("pkgNo", orderNo);

            // 根据邮编判断是否返回签收图
            if (hasZipMatch) {
                // 获取所有子单非空签收图
                List<String> imageUrls = subOrders.stream()
                        .map(TmsCustomerOrderEntity::getDeliveryProof)
                        .filter(StrUtil::isNotBlank)
                        .distinct()
                        .collect(Collectors.toList());
                orderJo.set("images", imageUrls);
            }

            // 转换轨迹数据格式
            JSONArray trackJa = tracks.stream()
                    .filter(track -> track.getExternalDescription() != null) // 过滤空描述
                    .map(track -> {
                        JSONObject jo = new JSONObject();
                        jo.set("code", track.getStatusCode());
                        jo.set("time", DateUtil.format(track.getAddTime(), "yyyy-MM-dd HH:mm:ss"));
                        jo.set("status", StrUtil.blankToDefault(track.getExternalDescription(), ""));
                        jo.set("city", StrUtil.blankToDefault(track.getCity(), ""));
                        return jo;
                    }).collect(Collectors.toCollection(JSONArray::new));

            orderJo.set("track", trackJa);
            ja.add(orderJo);
        }
    }

    /**
     * 处理JY订单号的内部方法（直接匹配模式）
     * @param orders 查询到的订单列表
     * @param jyOrderNos JY订单号列表
     * @param zipList 邮编列表
     * @param hasZipMatch 是否有邮编匹配
     * @param ja 结果集合
     * @param handledPkgNos 已处理的包裹号集合
     */
    private void processJyOrdersInternal(List<TmsCustomerOrderEntity> orders, List<String> jyOrderNos,
                                       List<String> zipList, boolean hasZipMatch, JSONArray ja, Set<String> handledPkgNos) {
        // 处理TMS本地找到的JY订单
        for (TmsCustomerOrderEntity order : orders) {
            String matchNo = null;
            // 确定匹配的JY订单号
            if (jyOrderNos.contains(order.getJyOrderNo())) {
                matchNo = order.getJyOrderNo();
            }

            if (matchNo == null || handledPkgNos.contains(matchNo)) {
                continue;
            }

            // 邮编过滤
            if (hasZipMatch) {
                String destZip = StrUtil.blankToDefault(order.getDestPostalCode(), "").toUpperCase();
                if (!zipList.contains(destZip)) {
                    continue;
                }
            }

            String mainOrderNo = order.getEntrustedOrderNumber();
            // 查询轨迹数据
            if (order.getEntrustedOrderNumber()!=null&&order.getEntrustedOrderNumber().length()>15){
                mainOrderNo=order.getEntrustedOrderNumber().substring(0, order.getEntrustedOrderNumber().length()-3);
            }
            List<TmsOrderTrackEntity> tracks = orderTrackMapper.selectList(
                new LambdaQueryWrapper<TmsOrderTrackEntity>()
                    .eq(TmsOrderTrackEntity::getDelFlag, "0")
                    .likeRight(TmsOrderTrackEntity::getOrderNo, mainOrderNo)
                    .eq(TmsOrderTrackEntity::getTrackType, TrackTypeConstant.EXTERNAL)
                    .orderByDesc(TmsOrderTrackEntity::getAddTime));

            // 如果TMS本地有轨迹数据，则使用本地数据
            if (CollUtil.isNotEmpty(tracks) && tracks.size() >= 2)  {
                handledPkgNos.add(matchNo);

                JSONObject orderJo = new JSONObject();
                orderJo.set("pkgNo", matchNo);

                // 转换轨迹数据格式（使用与普通订单不同的格式，保持与原processJyOrders一致）
                JSONArray trackJa = tracks.stream()
                    .filter(track -> track.getLocationDescription() != null)
                    .map(track -> {
                        JSONObject jo = new JSONObject();
                        jo.set("time", DateUtil.format(track.getAddTime(), "yyyy-MM-dd HH:mm:ss"));
                        jo.set("timestamp", DateUtil.format(track.getAddTime(), "yyyy-MM-dd HH:mm:ss"));
                        jo.set("status", StrUtil.blankToDefault(track.getLocationDescription(), ""));
                        jo.set("address", StrUtil.blankToDefault(track.getCity(), ""));
                        return jo;
                    }).collect(Collectors.toCollection(JSONArray::new));

                orderJo.set("track", trackJa);

                // 根据邮编判断是否返回签收图
                if (hasZipMatch && StrUtil.isNotBlank(order.getDeliveryProof())) {
                    orderJo.set("images", Arrays.asList(order.getDeliveryProof()));
                }

                ja.add(orderJo);
                log.info("JY订单使用TMS本地轨迹：{}", matchNo);
            }
        }
    }


    /**
     * 调用佳邮接口查询轨迹
     * @param orderNos 订单号列表
     * @param ja 结果集合
     * @param handledPkgNos 已处理的包裹号集合
     */
    private void queryJyApiTracks(List<String> orderNos, JSONArray ja, Set<String> handledPkgNos) {
        if (CollUtil.isEmpty(orderNos)) {
            return;
        }

        try {
            // 构建请求
            HttpRequest request = HttpRequest.post(JY_API_URL);
            request.header("apiKey", JY_API_KEY);
            request.body(JSONUtil.toJsonStr(orderNos));

            // 发送请求
            HttpResponse response = request.execute();
            String result = response.body();
            log.info("佳邮接口返回结果：{}", result);

            // 解析响应
            JSONObject retJo = JSONUtil.parseObj(result);
            if (retJo.getStr("message").equals("success") && retJo.containsKey("data")) {
                JSONArray data = retJo.getJSONArray("data");

                for (int orderIndex = 0; orderIndex < data.size(); orderIndex++) {
                    JSONObject tracks = data.getJSONObject(orderIndex);
                    String pkgNo = tracks.getStr("trackingNo");

                    // 防止重复加入
                    if (handledPkgNos.contains(pkgNo)) {
                        continue;
                    }
                    handledPkgNos.add(pkgNo);

                    JSONObject orderJo = new JSONObject();
                    orderJo.set("pkgNo", pkgNo);

                    JSONArray fromDetails = tracks.getJSONArray("fromDetail");

                    if (fromDetails != null && fromDetails.size() > 0) {
                        JSONArray localJa = new JSONArray();

                        for (int i = 0; i < fromDetails.size(); i++) {
                            JSONObject item = fromDetails.getJSONObject(i);

                            JSONObject localJo = new JSONObject();
                            localJo.set("time", item.getStr("pathTime"));
                            localJo.set("timestamp", item.getStr("pathTime"));
                            localJo.set("status", item.getStr("pathInfo"));
                            localJo.set("address", item.getStr("pathLocation"));

                            localJa.add(localJo);
                        }

                        orderJo.set("track", localJa);

                        // 处理POD签收图片
                        JSONArray podsArray = tracks.getJSONArray("pods");
                        if (podsArray != null && !podsArray.isEmpty()) {
                            List<String> imageUrls = new ArrayList<>();
                            for (Object pod : podsArray) {
                                if (pod instanceof String) {
                                    String imageUrl = (String) pod;
                                    if (StrUtil.isNotBlank(imageUrl)) {
                                        imageUrls.add(imageUrl);
                                    }
                                }
                            }
                            if (!imageUrls.isEmpty()) {
                                orderJo.set("images", imageUrls);
                            }
                        }

                        ja.add(orderJo);
                        log.info("佳邮接口查询成功，订单号：{}，轨迹数量：{}", pkgNo, localJa.size());
                    } else {
                        log.warn("佳邮接口返回空轨迹，订单号：{}", pkgNo);
                    }
                }
            } else {
                log.error("佳邮接口返回错误：{}", retJo.getStr("message"));
            }

        } catch (Exception e) {
            log.error("调用佳邮接口异常，订单号：{}", orderNos, e);
        }
    }


//    public static void main(String[] args) {
//        double lat = 40.714224;
//        double lng = -73.961452;
//        String apiKey = "AIzaSyAJPHMzFIOVgBiyukRDkG9J91LVEDZvpDk";
//        String url = String.format(
//                "https://maps.googleapis.com/maps/api/geocode/json?latlng=%s,%s&key=%s",
//                lat, lng, apiKey
//        );
//        // 设置代理
//        Proxy proxy = new Proxy(Proxy.Type.HTTP, new InetSocketAddress("*************", 7890));
//        SimpleClientHttpRequestFactory requestFactory = new SimpleClientHttpRequestFactory();
//        //requestFactory.setProxy(proxy);
//
//        // 创建 RestTemplate 并注入代理工厂
//        RestTemplate restTemplate = new RestTemplate(requestFactory);
//        ResponseEntity<String> response = restTemplate.getForEntity(url, String.class);
//
//        if (response.getStatusCode().is2xxSuccessful()) {
//            System.out.println("返回结果：");
//            System.out.println(response.getBody());
//        } else {
//            System.out.println("请求失败，状态码: " + response.getStatusCode());
//        }
//    }

//    public static void main(String[] args) {
//        // 示例经纬度，可以替换为任意你想查询的值
//        double latitude = 37.7749;
//        double longitude = -122.4194;
//
//        // 构建 URL
//        String apiKey = "AIzaSyAJPHMzFIOVgBiyukRDkG9J91LVEDZvpDk";
//        String url = String.format("https://maps.googleapis.com/maps/api/geocode/json?latlng=%s,%s&key=%s",
//                latitude, longitude, apiKey);
//
//        // 设置代理信息
//        String proxyHost = "*************";
//        int proxyPort = 7890;
//
//        // 配置代理
//        Proxy proxy = new Proxy(Proxy.Type.HTTP, new InetSocketAddress(proxyHost, proxyPort));
//
//        // 创建 OkHttpClient 实例并设置代理
//        OkHttpClient client = new OkHttpClient.Builder()
//                //.proxy(proxy)
//                .build();
//
//        // 构建请求
//        Request request = new Request.Builder()
//                .url(url)
//                .get()
//                .build();
//
//        // 发送请求并处理响应
//        try (Response response = client.newCall(request).execute()) {
//            if (response.isSuccessful() && response.body() != null) {
//                System.out.println("响应内容：");
//                System.out.println(response.body().string());
//            } else {
//                System.err.println("请求失败，状态码: " + response.code());
//            }
//        } catch (IOException e) {
//            System.err.println("请求发生错误: " + e.getMessage());
//        }
//    }



}
