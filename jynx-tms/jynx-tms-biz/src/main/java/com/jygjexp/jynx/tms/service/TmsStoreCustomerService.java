package com.jygjexp.jynx.tms.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.jygjexp.jynx.admin.api.dto.ResetLoginPwdDTO;
import com.jygjexp.jynx.admin.api.dto.SysUserAddDto;
import com.jygjexp.jynx.common.core.util.R;
import com.jygjexp.jynx.tms.dto.StorePwdChangeDTO;
import com.jygjexp.jynx.tms.dto.TmsStoreCustomerDTO;
import com.jygjexp.jynx.tms.entity.TmsStoreCustomerEntity;
import com.jygjexp.jynx.tms.vo.StoreCustomerExtendVO;
import com.jygjexp.jynx.tms.vo.TmsStoreCustomerAdminVo;
import com.jygjexp.jynx.tms.vo.TmsStoreCustomerExcelVo;
import com.jygjexp.jynx.tms.vo.TmsStoreCustomerPageVo;

import java.util.List;

public interface TmsStoreCustomerService extends IService<TmsStoreCustomerEntity> {

    R saveStoreCustomer(TmsStoreCustomerDTO tmsStoreCustomer);

    R updateStoreCustomer(TmsStoreCustomerDTO tmsStoreCustomer);

    R updateStatus(TmsStoreCustomerEntity tmsStoreCustomer);
    /**
     * 获取门店客户消息通知配置
     *
     * @return
     */
    StoreCustomerExtendVO getMessageTraceConfig();

    /**
     * 更新门店客户消息通知配置
     *
     * @return
     */
    boolean updateMessageTraceConfig(StoreCustomerExtendVO customerExtendVO);

    /**
     * 获取邮件校验码
     *
     * @param email
     * @return
     */
    boolean getEmailCode(String email);
    /**
     * 更新邮箱
     *
     * @param email
     * @param code
     * @return
     */
    boolean updateEmail(String email, String code);


    /**
     * 手机号获取验证码
     *
     * @param phone
     * @return
     */
    boolean getPhoneCode(String phone);

    /**
     * 修改手机号
     *
     * @param phone
     * @param code
     * @return
     */
    boolean updatePhone(String phone, String code);

    /**
     * 登录页面重置密码
     *
     * @param resetLoginPwdDTO
     * @return
     */
    boolean updateLoginPwd(ResetLoginPwdDTO resetLoginPwdDTO);


    /**
     * 客户端修改密码
     *
     * @param storePwdChangeDTO
     * @return
     */
    boolean updatePwd(StorePwdChangeDTO storePwdChangeDTO);

    /**
     * 获取客户
     *
     * 用户id -> 客户
     * @param userId
     * @return
     */
    TmsStoreCustomerEntity getStoreCustomerByUserId(Long userId);
    /**
     * 获取客户id
     *
     * 用户id -> 客户id
     * @param userId
     * @return
     */
    Long getStoreCustomerIdByUserId(Long userId);

    /**
     * 客户id -> 客户
     * @param id
     * @return
     */
    TmsStoreCustomerEntity getStoreCustomerById(Long id);

    /**
     * 管理端分页查询
     *
     * @param page
     * @param tmsStoreCustomer
     * @return
     */
    R<IPage<TmsStoreCustomerAdminVo>> getTmsStoreCustomerAdminPage(Page page, TmsStoreCustomerPageVo tmsStoreCustomer);

    List<TmsStoreCustomerExcelVo> getTmsStoreCustomerAdminExcel(TmsStoreCustomerPageVo tmsStoreCustomer);

    R removeBatchCustomer(Long[] ids);

    List<TmsStoreCustomerEntity> getAllCustomersByUser();

    R register(SysUserAddDto user);


    /**
     * 获取当前门店客户
     *
     * @return
     */
    TmsStoreCustomerEntity getCurrentCustomer();

    R updateStoreCustomerPwd(TmsStoreCustomerDTO tmsStoreDTO);

    /**
     * 更新客户单量
     */
    void updateCustomerOrderCount(Long id, Integer orderCount);

}
