package com.jygjexp.jynx.tms.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.jygjexp.jynx.common.core.util.R;
import com.jygjexp.jynx.tms.dto.*;
import com.jygjexp.jynx.tms.entity.TmsStoreOrderEntity;
import com.jygjexp.jynx.tms.vo.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;


public interface TmsStoreOrderService extends IService<TmsStoreOrderEntity> {


    Page<StoreOrderVO> selectPage(Page page, StoreOrderQueryDTO storeOrderQueryDTO);

    boolean saveStoreOrder(StoreOrderDTO storeOrderDTO);

    boolean updateStoreOrderById(StoreOrderDTO storeOrderDTO);

    StoreOrderDetailVO getStoreOrderById(Long id);

    List<ImportStoreGoodsDTO> importStoreGoods(MultipartFile file);

    List<ChannelPriceVO> getChannelPrice(ChannelPriceQueryDTO channelPriceQueryDTO);

    SuggestStoreDetailVO getSuggestStore(SuggestStoreQueryDTO storeQueryDTO);

    void printScript(Long id,HttpServletResponse response);

    IPage<StoreOrderVO> getTmsStoreOrderStorePage(StoreOrderQueryDTO storeOrderQueryDTO);

    IPage<StoreOrderVO> getTmsStoreOrderAdminPage(StoreOrderQueryDTO storeOrderQueryDTO);

    R importCargoInfo(MultipartFile file);

    boolean cancelOrder(Long id);
    /**
     *
     * @param id
     * @return
     */
    boolean writeOffOrder(Long id);
}
