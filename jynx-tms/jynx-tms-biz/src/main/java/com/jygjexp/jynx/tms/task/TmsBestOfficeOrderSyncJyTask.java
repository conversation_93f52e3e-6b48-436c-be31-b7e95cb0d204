package com.jygjexp.jynx.tms.task;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.jygjexp.jynx.common.core.util.R;
import com.jygjexp.jynx.tms.entity.TmsCargoInfoEntity;
import com.jygjexp.jynx.tms.entity.TmsCustomerEntity;
import com.jygjexp.jynx.tms.entity.TmsCustomerOrderEntity;
import com.jygjexp.jynx.tms.entity.TmsThirdPartPostEntity;
import com.jygjexp.jynx.tms.enums.NewOrderStatus;
import com.jygjexp.jynx.tms.mapper.TmsCustomerOrderMapper;
import com.jygjexp.jynx.tms.service.TmsCargoInfoService;
import com.jygjexp.jynx.tms.service.TmsCustomerOrderService;
import com.jygjexp.jynx.tms.service.TmsOrderLogService;
import com.jygjexp.jynx.tms.service.TmsOverAreaService;
import com.jygjexp.jynx.tms.utils.OkHttpUtil;
import com.jygjexp.jynx.tms.utils.OrderTools;
import com.jygjexp.jynx.zxoms.dto.MerchantDto;
import com.jygjexp.jynx.zxoms.dto.OrderDto;
import com.jygjexp.jynx.zxoms.dto.OrderPathDto;
import com.jygjexp.jynx.zxoms.entity.*;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

/**
 * @Author: xiongpengfei
 * @Description: Best Office 客户【订单】推送佳邮系统
 * @Date: 2025/07/31 10:35
 */
@Slf4j
@RequiredArgsConstructor
@Component
public class TmsBestOfficeOrderSyncJyTask {
    private final TmsCustomerOrderMapper customerOrderMapper;
    private final TmsOverAreaService tmsOverAreaService;
    private final TmsCargoInfoService  cargoInfoService;
    private final TmsOrderLogService tmsOrderLogService;
    private final static Integer ISPUSH=2;   // 表示推送佳邮

    @SneakyThrows
    @XxlJob(value = "bestOfficeOrderSyncJyTask")
    public void BestOfficeOrderSyncJyTask() {
        //String param = XxlJobHelper.getJobParam();
        XxlJobHelper.log("定时任务：Best Office 客户【订单】推送佳邮系统于:{}，输入参数{}", LocalDateTime.now(), "运行中");
        Map<Integer, JSONObject> provinceMap = new HashMap<>();

        MPJLambdaWrapper<TmsCustomerOrderEntity> wrapper = new MPJLambdaWrapper<>();
        wrapper.selectAll(TmsCustomerOrderEntity.class)
                .eq(TmsCustomerOrderEntity::getSubFlag, false)
                .eq(TmsCustomerOrderEntity::getDelFlag, "0")
                .isNull(TmsCustomerOrderEntity::getJyOrderNo)
                .gt(TmsCustomerOrderEntity::getCreateTime, DateUtil.offsetDay(new Date(), -1))
                .leftJoin(TmsCustomerEntity.class, TmsCustomerEntity::getId, TmsCustomerOrderEntity::getCustomerId)
                .like(TmsCustomerEntity::getIsPush, ISPUSH)
                .orderByDesc(TmsCustomerOrderEntity::getCreateTime)
                .last("limit 50")
        ;
        List<TmsCustomerOrderEntity> orderList = customerOrderMapper.selectJoinList(TmsCustomerOrderEntity.class,wrapper);


        if (CollUtil.isNotEmpty(orderList)){
            // 推送订单
            syncCreateOrder(orderList);
        }


        XxlJobHelper.handleSuccess(); // 设置任务结果
        XxlJobHelper.log("定时任务：Best Office 客户【订单】推送佳邮系统执行结束，时间: {}", LocalDateTime.now());
    }


    public R syncCreateOrder(List<TmsCustomerOrderEntity> customerOrder) {
        String url = "https://api.jygjexp.com/v1/api/orderNew/createOrder";

        for (TmsCustomerOrderEntity order : customerOrder) {
            // 需要判断订单配送范围是否在NB配送范围内，在则选择JY118渠道，否则选择LT051
            String channelCode = "JY118";
            String routeNo1 = tmsOverAreaService.getRouteNo(order.getShipperPostalCode());
            String routeNo2 = tmsOverAreaService.getRouteNo(order.getDestPostalCode());
            if (StringUtils.isBlank(routeNo1) || StringUtils.isBlank(routeNo2)) {
                channelCode = "LT051";
            }

            // 查询当前主单下的所有子单（箱号）
            LambdaQueryWrapper<TmsCargoInfoEntity> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.likeRight(TmsCargoInfoEntity::getEntrustedOrderNumber, order.getEntrustedOrderNumber());
            List<TmsCargoInfoEntity> cargoInfos = cargoInfoService.list(queryWrapper);

            for (TmsCargoInfoEntity subOrder : cargoInfos) {
                //构建数据
                HashMap<String, Object> dateMap = new HashMap<>();
                dateMap.put("channelCode", channelCode);
                // 使用子单号作为 referenceNo 和 trackingNo，保持和子单唯一
                dateMap.put("referenceNo", subOrder.getEntrustedOrderNumber());
                //dateMap.put("trackingNo", subOrder.getEntrustedOrderNumber());
                dateMap.put("productType", "1");
                dateMap.put("pweight", formatDecimal(subOrder.getWeight()));
                dateMap.put("pieces", subOrder.getCargoQuantity());
                dateMap.put("insured", "0");
                dateMap.put("consigneeName", order.getReceiverName());
                dateMap.put("consigneeCountryCode", "CA");

                // 获取收货地省份与城市
                String[] split = order.getDestination().split("/");
                String province = null;
                String city = null;
                if (split.length >= 3) {
                    province = split[1];
                    city = split[2];
                }
                dateMap.put("consigneeProvince", province);
                dateMap.put("consigneeCity", city);
                dateMap.put("consigneeAddress", order.getDestAddress());
                dateMap.put("consigneePostcode", order.getDestPostalCode());
                dateMap.put("consigneeMobile", order.getReceiverPhone());
                dateMap.put("consigneePhone", order.getReceiverPhone());
                dateMap.put("consigneeEmail", "");
                dateMap.put("shipperName", order.getShipperName());
                dateMap.put("shipperCountryCode", "CA");

                // 发货地省份城市
                String[] splitTwo = order.getOrigin().split("/");
                String provinceTwo = null;
                String cityTwo = null;
                if (splitTwo.length >= 3) {
                    provinceTwo = splitTwo[1];
                    cityTwo = splitTwo[2];
                }
                dateMap.put("shipperProvince", provinceTwo);
                dateMap.put("shipperCity", cityTwo);
                dateMap.put("shipperAddress", order.getShipperAddress());
                dateMap.put("shipperPostcode", order.getShipperPostalCode());
                dateMap.put("shipperPhone", order.getShipperPhone());
                dateMap.put("currencyCode", "USD");
                dateMap.put("returnLabel", "1");

                // 商品信息
                HashMap<String, Object> apiList = new HashMap<>();
                apiList.put("cname", "衣服");
                apiList.put("ename", "jeans");
                apiList.put("price", "1");
                apiList.put("quantity", subOrder.getCargoQuantity());
                apiList.put("sku", "0000001234");
                apiList.put("unitCode", "PCE");
                apiList.put("weight", formatDecimal(subOrder.getWeight()));
                dateMap.put("apiOrderItemList", apiList);

                // 每个请求只传单个箱子
                List<Map<String, Object>> boxList = new ArrayList<>();
                Map<String, Object> box = new HashMap<>();
                box.put("length", formatDecimal(subOrder.getLength()));
                box.put("width", formatDecimal(subOrder.getWidth()));
                box.put("height", formatDecimal(subOrder.getHeight()));
                box.put("rweight", formatDecimal(subOrder.getWeight()));
                box.put("quantity", subOrder.getCargoQuantity());
                box.put("bagNum", subOrder.getEntrustedOrderNumber());
                boxList.add(box);
                dateMap.put("apiOrderVolumeList", boxList);

                org.json.JSONObject jsonObject = new org.json.JSONObject(dateMap);
                HashMap<String, String> headerMap = new HashMap<>();
                //headerMap.put("code", "220999");
                headerMap.put("code", "test");
//                String apiKey = "198e900a5a66403b86f6c916d05b43ae";
                String apiKey = "1958538970ce46b79081437d8d3d35b4";  // 测试key
                headerMap.put("apiKey", apiKey);
                headerMap.put("Content-Type", "application/json");

                LocalDateTime now = LocalDateTime.now();
                DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
                String osName = System.getProperty("os.name").toLowerCase();
                if (!osName.contains("win")) {
                    now = now.plusHours(12);
                }
                headerMap.put("timestamp", now.format(formatter));
                headerMap.put("sign", OrderTools.getMD5("test" + apiKey));

                try {
                    String result = OkHttpUtil.doPostJson(url, jsonObject.toString(), headerMap);
                    ObjectMapper mapper = new ObjectMapper();
                    JsonNode node = mapper.readTree(result);
                    if (node.has("code") && node.get("code").asInt() == 1 && node.has("data")) {
                        JsonNode dataNode = node.get("data");
                        String trackingNo = dataNode.get("trackingNo").asText();
                        String labelPath = dataNode.get("labelPath").asText();

                        // 将信息记录订单表中的推送字段
/*                        customerOrderMapper.update(null, new LambdaUpdateWrapper<TmsCustomerOrderEntity>()
                                .eq(TmsCustomerOrderEntity::getEntrustedOrderNumber, subOrder.getEntrustedOrderNumber())
                                .eq(TmsCustomerOrderEntity::getSubFlag, Boolean.TRUE)
                                .set(TmsCustomerOrderEntity::getJyOrderNo, trackingNo)
                                .set(TmsCustomerOrderEntity::getPushLabel, labelPath)
                        );*/

                        // 记录成功日志
                        tmsOrderLogService.saveLog(subOrder.getEntrustedOrderNumber(), 266, "Best Office推送小包成功, 订单号: " + trackingNo + "返回信息： " + result, "");

                        // 回传Jy单号
                        order.setJyOrderNo(trackingNo);
                        customerOrderMapper.updateById(order);
                    } else {
                        tmsOrderLogService.saveLog(subOrder.getEntrustedOrderNumber(), 500, "Best Office推送小包失败, 返回信息： " + result, "");
                    }
                } catch (IOException e) {
                    e.printStackTrace();
                    // 记录异常日志
                    tmsOrderLogService.saveLog(subOrder.getEntrustedOrderNumber(), 500, "Best Office推送小包异常: " + e.getMessage(), "");
                }
            }
        }
        return R.ok("推送完成");
    }


    // 校验货物信息，小数点位数
    private BigDecimal formatDecimal(BigDecimal value) {
        if (value == null) {
            return BigDecimal.ZERO.setScale(2, RoundingMode.HALF_UP);
        }
        return value.setScale(2, RoundingMode.HALF_UP);
    }


}
